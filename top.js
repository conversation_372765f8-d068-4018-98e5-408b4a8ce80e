/**
 * 微信小程序
 * 作者：Tianxx
 * 版本：1.0
 * 日期：2025-07-21
 */
const NOTICE_SWITCH = 1; // 通知开关：1=开启，0=关闭
// 常量配置
const APPID = 'wx71a6af1f91734f18'; // 滔博运动小程序appid
const BRAND_CODE = 'TS'; // 品牌代码
const VERSION = '4.5.2'; // 应用版本
const ACTIVITY_ID = '0ae7d533258944bdae0aa23ce55925ec'; // 签到活动ID

// 解析命令行参数
const args = process.argv.slice(2);
const getArg = (name) => {
    const index = args.indexOf(`--${name}`);
    return index !== -1 && args[index + 1] ? args[index + 1] : null;
};

// 环境变量和命令行参数
const cmdWxid = getArg('wxid');
const isDebug = args.includes('--debug');
const wxidList = cmdWxid || process.env.TXX_WXID || '';

// 解析wxid列表的函数
function parseWxidList(wxidString) {
    if (!wxidString) return [];

    return wxidString
        .split('\n')                    
        .map(wxid => wxid.trim())       
        .filter(wxid => wxid.length > 0) 
        .filter(wxid => !wxid.startsWith('#')); 
}

// 引入wxcode模块
const wxcode = require('./wxcode');
const fs = require('fs');
const path = require('path');
const request = require('request');

// 获取脚本名称（不含扩展名）
const scriptName = path.basename(__filename, '.js');
// Token缓存文件路径
const TOKEN_CACHE_FILE = path.join(__dirname, `${scriptName}_tokens.json`);

class ScriptTemplate {
    constructor(wxid) {
        this.wxid = wxid;
        this.appid = APPID;
        this.isLogin = false;
        this.wxCode = null;
        this.openid = null;
        this.unionid = null;
        this.mobileInfo = null;
        this.userProfile = null;
        this.cacheExpireTime = null;
        // 滔博运动相关token
        this.tempToken = null;
        this.finalToken = null;
        this.memberId = null;
        this.qzSid = null;
    }

    // 读取token缓存
    loadTokenCache() {
        try {
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                const cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                const userCache = cacheData[this.wxid];

                if (userCache && userCache.cacheExpireTime > Date.now()) {
                    this.wxCode = userCache.wxCode;
                    this.openid = userCache.openid;
                    this.unionid = userCache.unionid;
                    this.mobileInfo = userCache.mobileInfo;
                    this.userProfile = userCache.userProfile;
                    this.tempToken = userCache.tempToken;
                    this.finalToken = userCache.finalToken;
                    this.memberId = userCache.memberId;
                    this.qzSid = userCache.qzSid;
                    this.cacheExpireTime = userCache.cacheExpireTime;
                    this.isLogin = true;

                    if (isDebug) {
                        console.log(`[DEBUG] 从缓存加载数据成功`);
                        console.log(`[DEBUG] 微信Code: ${this.wxCode}`);
                        console.log(`[DEBUG] OpenID: ${this.openid}`);
                        console.log(`[DEBUG] 缓存过期时间: ${new Date(this.cacheExpireTime).toLocaleString()}`);
                    }
                    return true;
                } else if (userCache) {
                    if (isDebug) console.log(`[DEBUG] 缓存数据已过期`);
                }
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 读取缓存失败: ${error.message}`);
        }
        return false;
    }

    // 保存数据到缓存
    saveTokenCache() {
        try {
            let cacheData = {};

            // 读取现有缓存
            if (fs.existsSync(TOKEN_CACHE_FILE)) {
                try {
                    cacheData = JSON.parse(fs.readFileSync(TOKEN_CACHE_FILE, 'utf8'));
                } catch (e) {
                    if (isDebug) console.log(`[DEBUG] 现有缓存文件格式错误，将重新创建`);
                }
            }

            // 设置缓存过期时间（默认2小时）
            const expireTime = Date.now() + (2 * 60 * 60 * 1000);

            // 更新当前用户的缓存信息
            cacheData[this.wxid] = {
                wxCode: this.wxCode,
                openid: this.openid,
                unionid: this.unionid,
                mobileInfo: this.mobileInfo,
                userProfile: this.userProfile,
                tempToken: this.tempToken,
                finalToken: this.finalToken,
                memberId: this.memberId,
                qzSid: this.qzSid,
                cacheExpireTime: expireTime,
                updateTime: Date.now()
            };

            this.cacheExpireTime = expireTime;

            // 写入文件
            fs.writeFileSync(TOKEN_CACHE_FILE, JSON.stringify(cacheData, null, 2), 'utf8');

            if (isDebug) {
                console.log(`[DEBUG] 缓存保存成功`);
                console.log(`[DEBUG] 缓存文件: ${TOKEN_CACHE_FILE}`);
                console.log(`[DEBUG] 过期时间: ${new Date(expireTime).toLocaleString()}`);
            }
        } catch (error) {
            console.log(`❌ 保存缓存失败: ${error.message}`);
        }
    }

    // 获取微信授权码并登录
    async getWxCodeAndLogin() {
        if (isDebug) console.log(`[DEBUG] 开始获取微信授权码...`);

        const codeResult = await wxcode.getWxCode(this.wxid, this.appid);
        if (!codeResult.success) {
            console.log(`获取授权码失败：${codeResult.error}`);
            return false;
        }

        this.wxCode = codeResult.code;
        if (isDebug) console.log(`[DEBUG] 获取授权码成功：${this.wxCode}`);

        this.isLogin = true;
        return true;
    }

    // 获取用户openid
    async getUserOpenid() {
        const result = await wxcode.getOpenid(this.wxid, this.appid);
        if (result.success) {
            this.openid = result.openid;
            if (isDebug) console.log(`[DEBUG] 获取openid成功：${this.openid}`);
            return this.openid;
        } else {
            console.log(`获取openid失败：${result.error}`);
            return null;
        }
    }

    // 获取手机号
    async getMobileInfo() {
        const result = await wxcode.getmobile(this.wxid, this.appid);
        if (result.success) {
            this.mobileInfo = result;
            if (isDebug) console.log(`[DEBUG] 获取手机号加密数据成功`);
            return this.mobileInfo;
        } else {
            console.log(`获取手机号失败：${result.error}`);
            return null;
        }
    }

    // 获取用户个人信息（云函数调用）
    async getUserProfile() {
        const cloudFunctionData = JSON.stringify({
            "api_name": "webapi_getuserprofile",
            "data": {
                "app_version": 68,
                "desc": "用于获取您的个人信息",
                "lang": "en",
                "version": "3.7.12"
            },
            "env": 1,
            "operate_directly": false,
            "show_confirm": true,
            "tid": Date.now() * 1000000 + Math.floor(Math.random() * 1000000), // 生成唯一tid
            "with_credentials": true
        });

        const result = await wxcode.getUserInfo(this.wxid, this.appid, cloudFunctionData);
        if (result.success) {
            if (isDebug) console.log(`[DEBUG] 获取用户个人信息成功`);
            // 解析用户信息
            try {
                const userInfo = JSON.parse(result.rawData.data);
                if (isDebug) {
                    console.log(`[DEBUG] 用户信息:`, {
                        nickName: userInfo.nickName,
                        gender: userInfo.gender,
                        avatarUrl: userInfo.avatarUrl,
                        city: userInfo.city,
                        province: userInfo.province,
                        country: userInfo.country
                    });
                }
                this.userProfile = {
                    success: true,
                    userInfo: userInfo,
                    signature: result.signature,
                    encryptedData: result.encryptedData,
                    iv: result.iv
                };
                return this.userProfile;
            } catch (e) {
                console.log(`解析用户信息失败：${e.message}`);
                return { success: false, error: e.message };
            }
        } else {
            console.log(`获取用户个人信息失败：${result.error}`);
            return null;
        }
    }

    // HTTP请求封装
    async makeRequest(options) {
        // 添加gzip支持
        options.gzip = true;
        options.encoding = null; // 获取原始buffer

        return new Promise((resolve, reject) => {
            request(options, (error, response, body) => {
                if (error) {
                    reject(error);
                    return;
                }

                try {
                    // 处理gzip压缩的响应
                    let bodyStr = body;
                    if (Buffer.isBuffer(body)) {
                        bodyStr = body.toString('utf8');
                    }

                    const data = typeof bodyStr === 'string' ? JSON.parse(bodyStr) : bodyStr;
                    resolve({
                        statusCode: response.statusCode,
                        headers: response.headers,
                        data: data
                    });
                } catch (e) {
                    resolve({
                        statusCode: response.statusCode,
                        headers: response.headers,
                        data: body
                    });
                }
            });
        });
    }

    // 获取OpenID（使用wxcode模块）
    async getOpenIdFromWxcode() {
        if (isDebug) console.log(`[DEBUG] 通过wxcode获取OpenID...`);

        const result = await wxcode.getOpenid(this.wxid, this.appid);
        if (result.success) {
            this.openid = result.openid;
            if (isDebug) console.log(`[DEBUG] 获取OpenID成功: ${this.openid}`);
            return true;
        } else {
            console.log(`❌ 获取OpenID失败：${result.error}`);
            return false;
        }
    }

    // 滔博运动初次登录 - 使用新的授权码
    async topsportsInitialLogin() {
        if (isDebug) console.log(`[DEBUG] 滔博运动初次登录...`);

        // 重新获取一个新的授权码用于滔博登录
        const newCodeResult = await wxcode.getWxCode(this.wxid, this.appid);
        if (!newCodeResult.success) {
            console.log(`❌ 获取新授权码失败：${newCodeResult.error}`);
            return false;
        }

        const newCode = newCodeResult.code;
        if (isDebug) console.log(`[DEBUG] 获取新授权码成功：${newCode}`);

        try {
            const options = {
                method: 'GET',
                url: `https://wxmall.topsports.com.cn/shopMember/auth/wxMiniProgram/login?code=${newCode}`,
                headers: {
                    'Host': 'wxmall.topsports.com.cn',
                    'Connection': 'keep-alive',
                    'version': VERSION,
                    'xweb_xhr': '1',
                    'brandCode': BRAND_CODE,
                    'appId': this.appid,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                    'Content-Type': 'application/json',
                    'Accept': '*/*',
                    'Referer': `https://servicewechat.com/${this.appid}/570/page-frame.html`
                }
            };

            const response = await this.makeRequest(options);
            if (isDebug) {
                console.log(`[DEBUG] 滔博运动初次登录响应:`, response);
            }

            if (response && response.data && response.data.code === 1 && response.data.data && response.data.data.success) {
                this.tempToken = response.data.data.token;
                if (isDebug) console.log(`[DEBUG] 获取临时Token成功: ${this.tempToken}`);
                return true;
            } else {
                console.log(`❌ 滔博运动初次登录失败`);
                if (response && response.data) {
                    console.log(`❌ 错误信息: ${response.data.bizMsg || '未知错误'}`);
                }
                return false;
            }
        } catch (error) {
            console.log(`❌ 滔博运动初次登录出错: ${error.message}`);
            return false;
        }
    }

    // 滔博运动注册绑定登录
    async topsportsRegisterAndLogin() {
        if (isDebug) console.log(`[DEBUG] 滔博运动注册绑定登录...`);

        // 首先获取手机号加密数据
        const mobileResult = await this.getMobileInfo();
        if (!mobileResult) {
            console.log(`❌ 获取手机号加密数据失败`);
            return false;
        }

        try {
            const requestBody = {
                encryptedData: mobileResult.encryptedData,
                iv: mobileResult.iv,
                optag: this.openid,
                code: mobileResult.code
            };

            const options = {
                method: 'POST',
                url: 'https://wxmall.topsports.com.cn/shopMember/auth/wxMiniProgram/registerBindAndLoginV1',
                headers: {
                    'Host': 'wxmall.topsports.com.cn',
                    'Connection': 'keep-alive',
                    'Content-Length': JSON.stringify(requestBody).length,
                    'version': VERSION,
                    'xweb_xhr': '1',
                    'brandCode': BRAND_CODE,
                    'appId': this.appid,
                    'Authorization': `Bearer ${this.tempToken}`,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                    'Content-Type': 'application/json',
                    'Accept': '*/*',
                    'Referer': `https://servicewechat.com/${this.appid}/570/page-frame.html`
                },
                body: JSON.stringify(requestBody)
            };

            const response = await this.makeRequest(options);
            if (isDebug) {
                console.log(`[DEBUG] 滔博运动注册绑定响应:`, response);
            }

            if (response && response.data && response.data.code === 1 && response.data.data && response.data.data.success) {
                this.finalToken = response.data.data.token;
                this.memberId = response.data.data.memberId;

                // 从响应头中提取QZ_SID
                const setCookieHeader = response.headers['set-cookie'];
                if (setCookieHeader) {
                    for (const cookie of setCookieHeader) {
                        if (cookie.includes('QZ_SID=')) {
                            this.qzSid = cookie.split('QZ_SID=')[1].split(';')[0];
                            break;
                        }
                    }
                }

                if (isDebug) {
                    console.log(`[DEBUG] 获取最终Token成功: ${this.finalToken}`);
                    console.log(`[DEBUG] 获取会员ID成功: ${this.memberId}`);
                    console.log(`[DEBUG] 获取QZ_SID成功: ${this.qzSid}`);
                }
                return true;
            } else {
                console.log(`❌ 滔博运动注册绑定登录失败`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 滔博运动注册绑定登录出错: ${error.message}`);
            return false;
        }
    }

    // 滔博运动签到
    async topsportsSignIn() {
        if (isDebug) console.log(`[DEBUG] 开始滔博运动签到...`);

        if (!this.finalToken || !this.memberId || !this.qzSid) {
            console.log(`❌ 签到所需的认证信息不完整`);
            return false;
        }

        try {
            const requestBody = {
                activityId: ACTIVITY_ID,
                brandCode: BRAND_CODE
            };

            // 构建Cookie字符串
            const cookieString = [
                `QZ_SID=${this.qzSid}`,
                `Authorization=${this.finalToken}`,
                `memberId=${this.memberId}`,
                `version=${VERSION}`,
                `appletsSource=${this.appid}`,
                `brandCode=${BRAND_CODE}`
            ].join('; ');

            const options = {
                method: 'POST',
                url: 'https://m.topsports.com.cn/h5/act/signIn/doSign',
                headers: {
                    'Host': 'm.topsports.com.cn',
                    'brandCode': BRAND_CODE,
                    'Accept': 'application/json, text/plain, */*',
                    'version': VERSION,
                    'Accept-Language': 'zh-CN,zh-Hans;q=0.9',
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Content-Type': 'application/json',
                    'Origin': 'https://m.topsports.com.cn',
                    'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.49(0x18003137) NetType/WIFI Language/zh_CN miniProgram/' + this.appid,
                    'Referer': `https://m.topsports.com.cn/m/dailycenter?brandCode=${BRAND_CODE}&share=true&minienv=1`,
                    'Content-Length': JSON.stringify(requestBody).length,
                    'Connection': 'keep-alive',
                    'Cookie': cookieString,
                    'Authorization': this.finalToken
                },
                body: JSON.stringify(requestBody)
            };

            const response = await this.makeRequest(options);
            if (isDebug) {
                console.log(`[DEBUG] 签到响应:`, response);
            }

            if (response && response.data && response.data.code === 1) {
                const signInData = response.data.data;
                if (signInData && signInData.signInSuccess) {
                    console.log(`✅ 签到成功！`);
                    console.log(`📝 ${signInData.signInTips}`);

                    // 显示奖励信息
                    if (signInData.signInPrizeSendResultDTOList && signInData.signInPrizeSendResultDTOList.length > 0) {
                        const prize = signInData.signInPrizeSendResultDTOList[0];
                        console.log(`🎁 获得奖励: ${prize.prizeName} ${prize.prizeValue}`);
                        print(`[${this.wxid}] 签到成功，获得${prize.prizeName} ${prize.prizeValue}`, true);
                    }
                    return true;
                } else {
                    // 检查是否是已经签到的情况
                    if (response.data.bizCode === 61205 || response.data.bizMsg.includes('已签到')) {
                        console.log(`ℹ️ 今日已签到: ${response.data.bizMsg}`);
                        print(`[${this.wxid}] 今日已签到`, true);
                        return true; // 已签到也算成功
                    } else {
                        console.log(`❌ 签到失败: ${response.data.bizMsg || '签到状态异常'}`);
                        return false;
                    }
                }
            } else {
                console.log(`❌ 签到请求失败: ${response && response.data ? response.data.bizMsg : '网络请求失败'}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 签到出错: ${error.message}`);
            return false;
        }
    }

    // 获取任务列表
    async getTopsportsTaskList() {
        if (isDebug) console.log(`[DEBUG] 获取任务列表...`);

        if (!this.finalToken || !this.memberId || !this.qzSid) {
            console.log(`❌ 获取任务列表所需的认证信息不完整`);
            return null;
        }

        try {
            // 构建Cookie字符串
            const cookieString = [
                `QZ_SID=${this.qzSid}`,
                `Authorization=${this.finalToken}`,
                `memberId=${this.memberId}`,
                `version=${VERSION}`,
                `appletsSource=${this.appid}`,
                `brandCode=${BRAND_CODE}`
            ].join('; ');

            const options = {
                method: 'GET',
                url: 'https://m.topsports.com.cn/h5/taskcenter/getTaskList?channelType=user_task_center&brandCode=TS',
                headers: {
                    'Host': 'm.topsports.com.cn',
                    'Connection': 'keep-alive',
                    'Accept': 'application/json, text/plain, */*',
                    'brandCode': BRAND_CODE,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                    'version': VERSION,
                    'Sec-Fetch-Site': 'same-origin',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    'Referer': `https://m.topsports.com.cn/m/dailycenter?brandCode=${BRAND_CODE}&share=true&minienv=1`,
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Cookie': cookieString
                }
            };

            const response = await this.makeRequest(options);
            if (isDebug) {
                console.log(`[DEBUG] 任务列表响应:`, response);
            }

            if (response && response.data && response.data.code === 1) {
                return response.data.data.list;
            } else {
                console.log(`❌ 获取任务列表失败: ${response && response.data ? response.data.bizMsg : '网络请求失败'}`);
                return null;
            }
        } catch (error) {
            console.log(`❌ 获取任务列表出错: ${error.message}`);
            return null;
        }
    }

    // 数据上报（访问自定义页面）- 微信小程序端
    async reportCustomPageVisit(topicCode) {
        if (isDebug) console.log(`[DEBUG] 上报自定义页面访问: ${topicCode}`);

        try {
            const requestBody = {
                data: {
                    browse_page: `topicCode=${topicCode}`
                },
                table: "e_visit_custom_page",
                topic: "crm_e_task_center_event_pro"
            };

            const options = {
                method: 'POST',
                url: 'https://wxmall.topsports.com.cn/shopMember/h5/taskcenter/dataReport',
                headers: {
                    'Host': 'wxmall.topsports.com.cn',
                    'Connection': 'keep-alive',
                    'Content-Length': JSON.stringify(requestBody).length,
                    'version': VERSION,
                    'xweb_xhr': '1',
                    'brandCode': BRAND_CODE,
                    'appId': this.appid,
                    'Authorization': `Bearer ${this.finalToken}`,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                    'Content-Type': 'application/json',
                    'Accept': '*/*',
                    'Sec-Fetch-Site': 'cross-site',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    'Referer': `https://servicewechat.com/${this.appid}/570/page-frame.html`,
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9'
                },
                body: JSON.stringify(requestBody)
            };

            const response = await this.makeRequest(options);
            if (isDebug) {
                console.log(`[DEBUG] 自定义页面访问上报响应:`, response);
            }

            if (response && response.data && response.data.code === 1 && response.data.bizCode === 20000) {
                return true;
            } else {
                console.log(`❌ 小程序端页面访问上报失败: ${response && response.data ? response.data.bizMsg : '网络请求失败'}`);
                return false;
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 自定义页面访问上报出错: ${error.message}`);
            return false;
        }
    }

    // 领取任务奖励
    async receiveTaskReward(taskId, taskDataId) {
        if (isDebug) console.log(`[DEBUG] 领取任务奖励: ${taskId}, ID: ${taskDataId}`);

        try {
            const requestBody = {
                id: taskDataId,
                taskId: taskId
            };

            // 构建Cookie字符串
            const cookieString = [
                `QZ_SID=${this.qzSid}`,
                `Authorization=${this.finalToken}`,
                `memberId=${this.memberId}`,
                `version=${VERSION}`,
                `appletsSource=${this.appid}`,
                `brandCode=${BRAND_CODE}`
            ].join('; ');

            const options = {
                method: 'POST',
                url: 'https://m.topsports.com.cn/h5/taskcenter/receiveReward',
                headers: {
                    'Host': 'm.topsports.com.cn',
                    'Connection': 'keep-alive',
                    'Content-Length': JSON.stringify(requestBody).length,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json, text/plain, */*',
                    'brandCode': BRAND_CODE,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                    'version': VERSION,
                    'Origin': 'https://m.topsports.com.cn',
                    'Sec-Fetch-Site': 'same-origin',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    'Referer': `https://m.topsports.com.cn/m/dailycenter?brandCode=${BRAND_CODE}&share=true&minienv=1`,
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Cookie': cookieString
                },
                body: JSON.stringify(requestBody)
            };

            const response = await this.makeRequest(options);
            if (isDebug) {
                console.log(`[DEBUG] 领取奖励响应:`, response);
            }

            if (response && response.data && response.data.code === 1) {
                // 检查具体的业务码
                if (response.data.bizCode === 20000) {
                    // 成功领取奖励
                    return { success: true, ...response.data.data };
                } else if (response.data.bizCode === 61203) {
                    // 奖励已经被领取过了
                    console.log(`ℹ️ 奖励已经被领取过了: ${response.data.bizMsg}`);
                    return { success: false, alreadyClaimed: true };
                } else {
                    console.log(`❌ 领取奖励失败: ${response.data.bizMsg}`);
                    return { success: false };
                }
            } else {
                console.log(`❌ 领取奖励失败: ${response && response.data ? response.data.bizMsg : '网络请求失败'}`);
                return { success: false };
            }
        } catch (error) {
            console.log(`❌ 领取奖励出错: ${error.message}`);
            return null;
        }
    }

    // 获取单个任务状态（刷新任务进度）
    async getOneTaskStatus(taskId) {
        if (isDebug) console.log(`[DEBUG] 获取任务状态: ${taskId}`);

        try {
            // 构建Cookie字符串
            const cookieString = [
                `QZ_SID=${this.qzSid}`,
                `Authorization=${this.finalToken}`,
                `memberId=${this.memberId}`,
                `version=${VERSION}`,
                `appletsSource=${this.appid}`,
                `brandCode=${BRAND_CODE}`
            ].join('; ');

            const options = {
                method: 'GET',
                url: `https://m.topsports.com.cn/h5/taskcenter/getOneTaskStatus?channelType=user_task_center&taskId=${taskId}&brandCode=${BRAND_CODE}`,
                headers: {
                    'Host': 'm.topsports.com.cn',
                    'Connection': 'keep-alive',
                    'Accept': 'application/json, text/plain, */*',
                    'brandCode': BRAND_CODE,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                    'version': VERSION,
                    'Sec-Fetch-Site': 'same-origin',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    'Referer': `https://m.topsports.com.cn/m/dailycenter?brandCode=${BRAND_CODE}&share=true&minienv=1`,
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Cookie': cookieString
                }
            };

            const response = await this.makeRequest(options);
            if (isDebug) {
                console.log(`[DEBUG] 任务状态响应:`, response);
            }

            if (response && response.data && response.data.code === 1) {
                return response.data.data;
            } else {
                console.log(`❌ 获取任务状态失败: ${response && response.data ? response.data.bizMsg : '网络请求失败'}`);
                return null;
            }
        } catch (error) {
            console.log(`❌ 获取任务状态出错: ${error.message}`);
            return null;
        }
    }



    // H5端浏览时长数据上报
    async reportBrowseDuration(duration = 15000, taskId = null, taskEventCode = null) {
        if (isDebug) console.log(`[DEBUG] 上报浏览时长...`);

        if (!this.finalToken || !this.memberId || !this.qzSid) {
            console.log(`❌ 浏览时长上报所需的认证信息不完整`);
            return false;
        }

        try {
            // 使用当前时间作为进入页面时间（与10积分成功案例一致）
            const currentTime = Date.now();
            const requestBody = {
                data: {
                    brand: BRAND_CODE,
                    browse_page: `https://m.topsports.com.cn/m/dailycenter?brandCode=${BRAND_CODE}&share=true&minienv=1`,
                    channel_type: "user_task_center",
                    into_page_time: currentTime
                },
                table: "e_browse_duration_tmp"
            };

            // 构建完整的Cookie字符串（与10积分成功案例一致）
            const cookieString = `appletsSource=${this.appid}; memberId=${this.memberId}; version=${VERSION}; sensorsdata2015jssdkcross=%7B%22%24device_id%22%3A%22198606a15e81a9-0e81ca7b784f39-673e1615-1440000-198606a15e9a7f%22%7D; sa_jssdk_2015_m_topsports_com_cn=%7B%22distinct_id%22%3A%22${this.memberId}%22%2C%22first_id%22%3A%22${this.memberId}%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk4NjA2YTE1ZTgxYTktMGU4MWNhN2I3ODRmMzktNjczZTE2MTUtMTQ0MDAwMC0xOTg2MDZhMTVlOWE3ZiIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjhhZmJkODE5OTg2MDAwNDMwMTk4NjA1MjZlYTcxMzAzIiwiJGlkZW50aXR5X2Fub255bW91c19pZCI6IjhhZmJkODE5OTg2MDAwNDMwMTk4NjA1MjZlYTcxMzAzIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22${this.memberId}%22%7D%7D; acw_tc=3ccdc14317539786200961437e2211c59955a2ad1c6b31874381b46e77a880; QZ_SID=${this.qzSid}; Authorization=${this.finalToken}`;

            const options = {
                method: 'POST',
                url: 'https://m.topsports.com.cn/h5/taskcenter/dataReport',
                headers: {
                    'Host': 'm.topsports.com.cn',
                    'Connection': 'keep-alive',
                    'Content-Length': JSON.stringify(requestBody).length,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json, text/plain, */*',
                    'brandCode': BRAND_CODE,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                    'version': VERSION,
                    'Origin': 'https://m.topsports.com.cn',
                    'Sec-Fetch-Site': 'same-origin',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    'Referer': `https://m.topsports.com.cn/m/dailycenter?brandCode=${BRAND_CODE}&share=true&minienv=1`,
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Cookie': cookieString
                },
                body: JSON.stringify(requestBody)
            };

            const response = await this.makeRequest(options);
            if (isDebug) {
                console.log(`[DEBUG] 浏览时长上报请求体:`, requestBody);
                console.log(`[DEBUG] 浏览时长上报响应:`, response);
            }

            if (response && response.data && response.data.code === 1 && response.data.bizCode === 20000) {
                return true;
            } else {
                console.log(`❌ H5端浏览时长上报失败: ${response && response.data ? response.data.bizMsg : '网络请求失败'}`);
                return false;
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 浏览时长上报出错: ${error.message}`);
            return false;
        }
    }

    // 处理任务中心任务
    async processTopsportsTasks() {
        console.log(`📋 开始处理任务中心任务...`);

        // 获取任务列表
        const taskList = await this.getTopsportsTaskList();
        if (!taskList || taskList.length === 0) {
            console.log(`❌ 未获取到任务列表`);
            return;
        }

        console.log(`📝 共找到 ${taskList.length} 个任务`);

        let completedTasks = 0;
        let earnedRewards = 0;

        for (const task of taskList) {
            try {
                console.log(`\n🎯 处理任务: ${task.taskName.replace(/<[^>]*>/g, '')}`);
                console.log(`📄 任务描述: ${task.taskDesc.replace(/<[^>]*>/g, '')}`);
                console.log(`📊 进度: ${task.completeGoalProgressBar}`);
                console.log(`📊 上限进度: ${task.completeUpperLimitProgressBar}`);
                console.log(`🎯 状态: ${task.taskStatus === 1 ? '已完成可领取' : task.taskStatus === 2 ? '进行中' : task.taskStatus === 3 ? '已领取奖励' : task.taskStatus === 301 ? '签到任务进行中' : '未开始'}`);

                let finalTaskData = task;
                let canReceiveReward = false;

                // 检查任务状态并处理
                if (task.taskStatus === 3) {
                    console.log(`✅ 任务奖励已领取，跳过`);
                    continue;
                } else if (task.taskStatus === 1) {
                    console.log(`✅ 任务已完成，可以直接领取奖励`);
                    canReceiveReward = true;
                } else if (task.taskStatus === 2 || task.taskStatus === 0) {
                    // 任务进行中或未开始，需要根据类型处理
                    console.log(`🔄 任务状态: ${this.getTaskStatusText(task.taskStatus)}，开始处理...`);

                    if (task.taskEventCode === 'e_browse_duration') {
                        // 浏览时长任务
                        const taskResult = await this.handleBrowseTask(task);
                        if (taskResult.success && taskResult.taskData) {
                            finalTaskData = taskResult.taskData;
                            canReceiveReward = taskResult.canReceiveReward;
                        }
                    } else if (task.taskEventCode === 'e_improve_user_info') {
                        // 完善用户信息任务
                        console.log(`ℹ️ 用户信息完善任务需要手动操作，跳过`);
                    } else if (task.taskEventCode === 'e_act_sign_in_success_sub') {
                        // 签到任务（已经在签到功能中处理）
                        console.log(`ℹ️ 签到任务已在签到功能中处理`);
                    } else {
                        console.log(`ℹ️ 未知任务类型: ${task.taskEventCode}，跳过`);
                    }
                } else {
                    console.log(`ℹ️ 任务状态不支持处理: ${this.getTaskStatusText(task.taskStatus)}`);
                }

                // 尝试领取奖励
                if (canReceiveReward) {
                    console.log(`🎁 尝试领取任务奖励...`);
                    const rewardResult = await this.receiveTaskReward(finalTaskData.taskId, finalTaskData.id);
                    if (rewardResult && rewardResult.success) {
                        console.log(`✅ 成功领取任务奖励`);
                        if (rewardResult.prizeList && rewardResult.prizeList.length > 0) {
                            const prize = rewardResult.prizeList[0];
                            console.log(`🎁 获得奖励: ${prize.prizeName} ${prize.prizeValue}`);
                        }
                        earnedRewards++;
                        print(`[${this.wxid}] 完成任务: ${task.taskName.replace(/<[^>]*>/g, '')}`, true);
                    } else {
                        console.log(`❌ 领取奖励失败`);
                    }
                } else {
                    console.log(`ℹ️ 任务暂时无法领取奖励`);
                }

                completedTasks++;

                // 任务间延迟
                await new Promise(resolve => setTimeout(resolve, 2000));

            } catch (error) {
                console.log(`❌ 处理任务失败: ${error.message}`);
            }
        }

        console.log(`\n📊 任务处理完成:`);
        console.log(`✅ 处理任务数: ${completedTasks}`);
        console.log(`🎁 获得奖励数: ${earnedRewards}`);

        if (earnedRewards > 0) {
            print(`[${this.wxid}] 任务中心完成 ${earnedRewards} 个任务`, true);
        }
    }

    // 接收任务（必须先接收任务才能完成）
    async receiveTask(taskId) {
        if (isDebug) console.log(`[DEBUG] 接收任务: ${taskId}`);

        if (!this.finalToken || !this.memberId || !this.qzSid) {
            console.log(`❌ 接收任务所需的认证信息不完整`);
            return false;
        }

        try {
            const cookieString = `appletsSource=${this.appid}; memberId=${this.memberId}; version=${VERSION}; sensorsdata2015jssdkcross=%7B%22%24device_id%22%3A%22198606a15e81a9-0e81ca7b784f39-673e1615-1440000-198606a15e9a7f%22%7D; sa_jssdk_2015_m_topsports_com_cn=%7B%22distinct_id%22%3A%22${this.memberId}%22%2C%22first_id%22%3A%22${this.memberId}%22%2C%22props%22%3A%7B%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk4NjA2YTE1ZTgxYTktMGU4MWNhN2I3ODRmMzktNjczZTE2MTUtMTQ0MDAwMC0xOTg2MDZhMTVlOWE3ZiIsIiRpZGVudGl0eV9sb2dpbl9pZCI6IjhhZmJkODE5OTg2MDAwNDMwMTk4NjA1MjZlYTcxMzAzIiwiJGlkZW50aXR5X2Fub255bW91c19pZCI6IjhhZmJkODE5OTg2MDAwNDMwMTk4NjA1MjZlYTcxMzAzIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22${this.memberId}%22%7D%7D; acw_tc=3ccdc14317539786200961437e2211c59955a2ad1c6b31874381b46e77a880; QZ_SID=${this.qzSid}; Authorization=${this.finalToken}`;

            const options = {
                method: 'POST',
                url: `https://m.topsports.com.cn/h5/taskcenter/receiveTask?taskId=${taskId}`,
                headers: {
                    'Host': 'm.topsports.com.cn',
                    'Connection': 'keep-alive',
                    'Content-Length': '0',
                    'Accept': 'application/json, text/plain, */*',
                    'brandCode': BRAND_CODE,
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090c37)XWEB/14185',
                    'version': VERSION,
                    'Origin': 'https://m.topsports.com.cn',
                    'Sec-Fetch-Site': 'same-origin',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    'Referer': `https://m.topsports.com.cn/m/dailycenter?brandCode=${BRAND_CODE}&share=true&minienv=1`,
                    'Accept-Encoding': 'gzip, deflate, br',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Cookie': cookieString
                }
            };

            const response = await this.makeRequest(options);
            if (isDebug) {
                console.log(`[DEBUG] 接收任务响应:`, response);
            }

            if (response && response.data && response.data.code === 1 && response.data.bizCode === 20000) {
                console.log(`✅ 成功接收任务`);
                return true;
            } else {
                console.log(`❌ 接收任务失败: ${response && response.data ? response.data.bizMsg : '网络请求失败'}`);
                return false;
            }
        } catch (error) {
            console.log(`❌ 接收任务出错: ${error.message}`);
            return false;
        }
    }

    // 处理浏览任务（重构版本，基于10积分文件夹的成功案例）
    async handleBrowseTask(task) {
        console.log(`🌐 处理浏览任务: ${task.taskName.replace(/<[^>]*>/g, '')}`);

        // 检查任务是否已经完成
        if (task.taskStatus === 1) {
            console.log(`✅ 浏览任务已完成，可以直接领取奖励`);
            return { success: true, canReceiveReward: true, taskData: task };
        }

        // 对于进行中的任务，也需要先接收任务（确保任务已被正确接收）
        if (task.taskStatus === 0 || task.taskStatus === 2) {
            console.log(`📝 ${task.taskStatus === 0 ? '任务未开始' : '任务进行中'}，先确保接收任务...`);
            const receiveResult = await this.receiveTask(task.taskId);
            if (!receiveResult) {
                console.log(`❌ 接收任务失败`);
                return { success: false, canReceiveReward: false };
            }
            // 接收任务后等待一下
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // 获取跳转链接信息
        const jumpInfo = task.jumpSceneInfoList && task.jumpSceneInfoList[0];
        if (!jumpInfo || !jumpInfo.jumpUrlPath) {
            console.log(`❌ 未找到浏览链接`);
            return { success: false, canReceiveReward: false };
        }

        // 从跳转链接中提取topicCode
        let topicCode = '';
        if (jumpInfo.jumpUrlPath.includes('topicCode=')) {
            const match = jumpInfo.jumpUrlPath.match(/topicCode=([^&]+)/);
            if (match) {
                topicCode = match[1];
            }
        }

        if (!topicCode) {
            console.log(`❌ 未找到topicCode`);
            return { success: false, canReceiveReward: false };
        }

        console.log(`📝 提取到topicCode: ${topicCode}`);
        console.log(`� 目标页面: ${jumpInfo.jumpUrlPath}`);

        // 模拟快速访问（2秒，符合你的实际操作）
        console.log(`⏱️ 模拟快速访问页面 2 秒...`);
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 按照10积分成功案例的完整流程
        console.log(`📊 开始完整的数据上报流程...`);

        // 第一步：微信小程序端自定义页面访问上报
        console.log(`📊 1. 微信小程序端页面访问上报...`);
        const customReport = await this.reportCustomPageVisit(topicCode);
        console.log(`📊 小程序端上报: ${customReport ? '成功' : '失败'}`);

        // 第二步：H5端浏览时长上报
        console.log(`📊 2. H5端浏览时长上报...`);
        const browseReport = await this.reportBrowseDuration(15000, task.taskId, task.taskEventCode);
        console.log(`📊 H5端上报: ${browseReport ? '成功' : '失败'}`);

        if (!customReport && !browseReport) {
            console.log(`❌ 所有数据上报都失败`);
            return { success: false, canReceiveReward: false };
        }

        console.log(`✅ 数据上报完成，开始刷新任务状态...`);

        // 模拟你的操作：第一次刷新通常无效果，需要等待几秒后再刷新
        let finalTask = task;
        let canReceiveReward = false;

        // 第一次刷新（通常无效果）
        console.log(`🔄 第一次刷新任务状态...`);
        let refreshedTask = await this.getOneTaskStatus(task.taskId);
        if (refreshedTask) {
            finalTask = refreshedTask;
            console.log(`📊 第一次刷新后状态: ${this.getTaskStatusText(refreshedTask.taskStatus)}`);
            canReceiveReward = refreshedTask.taskStatus === 1;
        }

        // 如果第一次刷新没有完成，等待几秒后再次刷新
        if (!canReceiveReward) {
            console.log(`⏳ 第一次刷新无效果，等待服务器处理...`);
            await new Promise(resolve => setTimeout(resolve, 3000));

            console.log(`🔄 第二次刷新任务状态...`);
            refreshedTask = await this.getOneTaskStatus(task.taskId);
            if (refreshedTask) {
                finalTask = refreshedTask;
                console.log(`📊 第二次刷新后状态: ${this.getTaskStatusText(refreshedTask.taskStatus)}`);
                console.log(`📊 完成进度: ${refreshedTask.completeUpperLimitProgressBar}`);
                canReceiveReward = refreshedTask.taskStatus === 1;
            }
        }

        // 如果还是没有完成，最后再试一次
        if (!canReceiveReward) {
            console.log(`⏳ 继续等待服务器处理...`);
            await new Promise(resolve => setTimeout(resolve, 2000));

            console.log(`🔄 最后一次刷新任务状态...`);
            refreshedTask = await this.getOneTaskStatus(task.taskId);
            if (refreshedTask) {
                finalTask = refreshedTask;
                console.log(`📊 最终状态: ${this.getTaskStatusText(refreshedTask.taskStatus)}`);
                console.log(`📊 完成进度: ${refreshedTask.completeUpperLimitProgressBar}`);
                canReceiveReward = refreshedTask.taskStatus === 1;
            }
        }

        if (canReceiveReward) {
            console.log(`✅ 任务完成，可以领取奖励！`);
        } else {
            console.log(`⚠️ 任务状态未更新为可领取，可能需要更多时间处理`);
        }

        return {
            success: true,
            canReceiveReward: canReceiveReward,
            taskData: finalTask
        };
    }

    // 获取任务状态文本描述
    getTaskStatusText(taskStatus) {
        switch (taskStatus) {
            case 0: return '未开始';
            case 1: return '已完成可领取';
            case 2: return '进行中';
            case 3: return '已领取奖励';
            default: return `未知状态(${taskStatus})`;
        }
    }

    // 验证缓存数据是否仍然有效
    async validateCache() {
        if (!this.isLogin || !this.finalToken) return false;

        if (isDebug) console.log(`[DEBUG] 验证缓存数据有效性...`);

        try {
            // 尝试进行签到来验证登录状态是否有效
            if (this.finalToken && this.memberId && this.qzSid) {
                if (isDebug) console.log(`[DEBUG] 缓存数据验证通过`);
                return true;
            }
        } catch (error) {
            if (isDebug) console.log(`[DEBUG] 缓存数据验证失败: ${error.message}`);
        }

        if (isDebug) console.log(`[DEBUG] 缓存数据已失效`);
        this.isLogin = false;
        return false;
    }

    // 执行完整的滔博运动登录流程
    async performFullLogin() {
        if (isDebug) console.log(`[DEBUG] 执行完整的滔博运动登录流程...`);

        // 1. 获取微信授权码
        const loginSuccess = await this.getWxCodeAndLogin();
        if (!loginSuccess) {
            console.log(`[${this.wxid}] 获取授权码失败，跳过`);
            return false;
        }

        // 2. 获取OpenID
        const openIdSuccess = await this.getOpenIdFromWxcode();
        if (!openIdSuccess) {
            console.log(`[${this.wxid}] 获取OpenID失败，跳过`);
            return false;
        }

        // 3. 滔博运动初次登录
        const initialLoginSuccess = await this.topsportsInitialLogin();
        if (!initialLoginSuccess) {
            console.log(`[${this.wxid}] 滔博运动初次登录失败，跳过`);
            return false;
        }

        // 4. 滔博运动注册绑定登录
        const registerSuccess = await this.topsportsRegisterAndLogin();
        if (!registerSuccess) {
            console.log(`[${this.wxid}] 滔博运动注册绑定失败，跳过`);
            return false;
        }

        // 5. 保存到缓存
        this.saveTokenCache();

        console.log(`✅ 滔博运动登录流程完成`);
        return true;
    }

    // 主要业务逻辑
    async run() {
        try {
            // 1. 尝试从缓存加载数据
            const cacheLoaded = this.loadTokenCache();

            if (cacheLoaded) {
                console.log(`📦 使用缓存的数据`);

                // 验证缓存数据是否仍然有效
                const cacheValid = await this.validateCache();
                if (!cacheValid) {
                    console.log(`⚠️ 缓存的数据已失效，重新获取...`);
                    const fullLoginSuccess = await this.performFullLogin();
                    if (!fullLoginSuccess) {
                        console.log(`[${this.wxid}] 完整登录失败，跳过`);
                        return;
                    }
                } else {
                    console.log(`✅ 缓存的数据有效`);
                }
            } else {
                // 2. 缓存无效或不存在，进行完整登录
                const fullLoginSuccess = await this.performFullLogin();
                if (!fullLoginSuccess) {
                    print(`[${this.wxid}] 完整登录失败，跳过`, true);
                    return;
                }
            }

            // 3. 执行签到
            console.log(`🎯 开始执行签到...`);
            const signInSuccess = await this.topsportsSignIn();
            if (signInSuccess) {
                console.log(`✅ 签到完成`);
            } else {
                console.log(`❌ 签到失败`);
                print(`[${this.wxid}] 签到失败`, true);
            }

            // 4. 处理任务中心任务
            console.log(`\n📋 开始处理任务中心...`);
            await this.processTopsportsTasks();
        } catch (error) {
            console.log(`[${this.wxid}] 脚本执行出错：${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }
    }
}

// 主函数
async function main() {
    console.log(`🔔 脚本开始执行`);
    
    if (isDebug) {
        console.log(`[DEBUG] 调试模式已开启`);
        console.log(`[DEBUG] APPID: ${APPID}`);
    }
    
    if (!wxidList) {
        console.log(`❌ 未设置环境变量 TXX_WXID 或命令行参数 --wxid`);
        return;
    }

    // 处理单个wxid或多个wxid
    const wxids = cmdWxid ? [cmdWxid] : parseWxidList(wxidList);

    if (wxids.length === 0) {
        console.log(`❌ 没有找到有效的wxid`);
        return;
    }

    console.log(`📋 共找到 ${wxids.length} 个有效账号`);

    if (isDebug) {
        console.log(`[DEBUG] 账号列表: ${wxids.join(', ')}`);
    }

    // 逐个处理账号
    for (let i = 0; i < wxids.length; i++) {
        const wxid = wxids[i];
        console.log(`\n🚀 [${i + 1}/${wxids.length}] 开始处理账号: ${wxid}`);

        try {
            const script = new ScriptTemplate(wxid);
            await script.run();
            console.log(`✅ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理完成`);
        } catch (error) {
            console.log(`❌ [${i + 1}/${wxids.length}] 账号 ${wxid} 处理失败: ${error.message}`);
            if (isDebug) {
                console.error(error);
            }
        }

        console.log('─'.repeat(60));

        // 如果不是最后一个账号，稍微延迟一下
        if (i < wxids.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    }

    console.log(`\n🎉 所有账号处理完成！`);

    // 发送通知
    if (NOTICE_SWITCH && notice) {
        await sendMsg(notice);
    }
}

// 通知相关变量和函数
let notice = '';

function print(msg, is_notice = false) {
    let str = `${msg}`;
    console.log(str);
    if (NOTICE_SWITCH && is_notice) {
        notice += `${str}\n`;
    }
}

async function sendMsg(message) {
    try {
        let notify = '';
        try {
            notify = require('./sendNotify');
        } catch (e) {
            try {
                notify = require("../sendNotify");
            } catch (e2) {
                console.log('❌ 未找到sendNotify模块，无法发送通知');
                return;
            }
        }
        await notify.sendNotify(scriptName, message);
        console.log('📢 通知发送成功');
    } catch (error) {
        console.log(`❌ 通知发送失败: ${error.message}`);
    }
}

// 执行脚本
main().catch(console.error);
